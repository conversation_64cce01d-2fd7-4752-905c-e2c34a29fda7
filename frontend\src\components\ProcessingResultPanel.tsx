import { useTTSStore } from '../store/ttsStore';
import { useState, useMemo } from 'react';
import { diffWords, type Change } from 'diff';

// 为AI返回的对话条目定义一个类型，增强代码可读性和健壮性
interface DialogueItem {
  speaker: string;
  emotion: string;
  content: string;
  type: string;
}

export default function ProcessingResultPanel() {
  const {
    isProcessingNovel,
    processingProgress,
    processingStatus,
    processingResult,
    processingError,
    resetNovelProcessing,
    novel,
  } = useTTSStore();

  const [isComparing, setIsComparing] = useState(false);

  const handleReset = () => {
    resetNovelProcessing();
  };

  const reconstructedText = useMemo(() => {
    if (!processingResult?.chapters) return '';
    return Object.values(processingResult.chapters)
      .flat()
      .map((item: any) => item.content)
      .join('\n');
  }, [processingResult]);

  const differences = useMemo(() => {
    if (!isComparing) return [];
    // 移除原始文本中的数字标记 (如 '1\n')，以便更准确地比较
    const cleanOriginalText = novel.content.replace(/^\d+\s*$/gm, '');
    return diffWords(cleanOriginalText, reconstructedText, {
      ignoreWhitespace: true,
    });
  }, [isComparing, novel.content, reconstructedText]);

  return (
    <div
      className="flex flex-col h-full bg-neutral-50 border-l border-neutral-200"
      data-oid="cj6zq94"
    >
      <div
        className="p-2 border-b border-neutral-200 flex justify-between items-center pl-[16px] pr-[16px] pt-[4px] pb-[4px]"
        data-oid="e9_r6h:"
      >
        <h2 className="text-sm font-semibold" data-oid="g85felo">
          AI 处理结果
        </h2>
        <div className="flex items-center space-x-4" data-oid="e99_d9d">
          {processingResult && (
            <label
              className="flex h-8 items-center cursor-pointer gap-2 px-2 rounded-lg hover:bg-neutral-100"
              data-oid="vj77erm"
            >
              <span className="text-sm" data-oid="kvw1e7h">
                对比差异
              </span>
              <input
                type="checkbox"
                className="toggle toggle-sm toggle-primary"
                checked={isComparing}
                onChange={(e) => setIsComparing(e.target.checked)}
                data-oid="e1hhs01"
              />
            </label>
          )}
          <button
            onClick={handleReset}
            className="btn btn-ghost btn-sm"
            data-oid="-iuxthx"
          >
            关闭
          </button>
        </div>
      </div>

      <div className="grow overflow-y-auto p-[8px]" data-oid="0u0o985">
        {processingError ? (
          <div className="alert alert-error" data-oid="0pwed-9">
            <div className="flex-1" data-oid="l-wo6.7">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                className="w-6 h-6 mx-2 stroke-current"
                data-oid="iaonty7"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636"
                  data-oid="vmbqcau"
                ></path>
              </svg>
              <label data-oid="ga7t7q.">{processingError}</label>
            </div>
          </div>
        ) : isProcessingNovel || processingResult ? (
          <div className="space-y-4" data-oid="lyyo_.s">
            {isProcessingNovel && (
              <>
                <div data-oid="8:3uifw">
                  <label
                    className="text-sm font-medium text-gray-700"
                    data-oid="3z7f2i:"
                  >
                    处理状态
                  </label>
                  <p
                    className="text-sm font-medium text-primary-600"
                    data-oid="ojt:m-c"
                  >
                    {processingStatus}
                  </p>
                </div>
                <div data-oid="::y:-x1">
                  <label
                    className="text-sm font-medium text-gray-700"
                    data-oid="23v86ho"
                  >
                    处理进度
                  </label>
                  <progress
                    className="progress progress-primary w-full"
                    value={processingProgress}
                    max="100"
                    data-oid="qxo_gmx"
                  ></progress>
                </div>
              </>
            )}

            {processingResult && (
              <div data-oid="lp8.kyz">
                <label
                  className="text-sm font-medium text-gray-700"
                  data-oid="9uec0vm"
                >
                  {isComparing ? '差异对比结果' : '最终结果'}
                </label>
                <div
                  className="w-full bg-white rounded-md p-4 text-sm border space-y-4 max-h-[calc(100vh-220px)] overflow-y-auto"
                  data-oid="f5y2b9x"
                >
                  {isComparing ? (
                    <div
                      className="whitespace-pre-wrap leading-relaxed"
                      data-oid="km0zb_a"
                    >
                      {differences.map((part: Change, index: number) => {
                        const style = {
                          backgroundColor: part.added
                            ? 'rgba(67, 177, 88, 0.2)'
                            : part.removed
                              ? 'rgba(239, 68, 68, 0.2)'
                              : 'transparent',
                          textDecoration: part.removed
                            ? 'line-through'
                            : 'none',
                          padding: '2px 0',
                          borderRadius: '3px',
                        };
                        return (
                          <span key={index} style={style} data-oid="cpg0rkl">
                            {part.value}
                          </span>
                        );
                      })}
                    </div>
                  ) : (
                    Object.entries(processingResult.chapters || {}).map(
                      ([chapterTitle, chapterContent], chapterIndex) => {
                        // 确保 chapterContent 是一个数组，以防数据格式意外错误
                        if (!Array.isArray(chapterContent)) {
                          return null;
                        }

                        return (
                          <div
                            key={chapterIndex}
                            className="space-y-3"
                            data-oid="zi2_43k"
                          >
                            <h3
                              className="text-base font-semibold border-b pb-2 mb-2"
                              data-oid="0zuxaa6"
                            >
                              {chapterTitle}
                            </h3>
                            {(chapterContent as DialogueItem[]).map(
                              (item, itemIndex) => (
                                <div
                                  key={itemIndex}
                                  className="p-3 rounded-md bg-neutral-50 border"
                                  data-oid="uxx3gw_"
                                >
                                  <p
                                    className="font-semibold text-primary-600"
                                    data-oid="2c02kjy"
                                  >
                                    {item.speaker}
                                    {item.emotion &&
                                      item.emotion !== 'neutral' && (
                                        <span
                                          className="text-xs font-normal text-neutral-500 ml-2"
                                          data-oid="0posmyf"
                                        >
                                          ({item.emotion})
                                        </span>
                                      )}
                                  </p>
                                  <p
                                    className="mt-1 text-neutral-800 whitespace-pre-wrap"
                                    data-oid=".0611ba"
                                  >
                                    {item.content}
                                  </p>
                                </div>
                              )
                            )}
                          </div>
                        );
                      }
                    )
                  )}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div
            className="text-center text-neutral-500 pt-16"
            data-oid="sg.cdyj"
          >
            <span
              className="loading loading-spinner loading-lg"
              data-oid="yt389f8"
            ></span>
            <p className="mt-4" data-oid=":.a2b3j">
              等待处理任务...
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
