import { useTTSStore } from '../store/ttsStore';
import { useMemo, useState, useEffect } from 'react';
import { diffWords, type Change } from 'diff';

// 为AI返回的对话条目定义一个类型，增强代码可读性和健壮性
interface DialogueItem {
  speaker: string;
  emotion: string;
  content: string;
  type: string;
}

// 情绪选项
const EMOTION_OPTIONS = [
  { value: 'neutral', label: '中性' },
  { value: 'happy', label: '开心' },
  { value: 'sad', label: '悲伤' },
  { value: 'angry', label: '愤怒' },
  { value: 'surprised', label: '惊讶' },
  { value: 'fearful', label: '恐惧' },
  { value: 'disgusted', label: '厌恶' },
  { value: 'excited', label: '兴奋' },
  { value: 'calm', label: '平静' },
  { value: 'confused', label: '困惑' },
];

export default function ProcessingResultPanel() {
  const {
    isProcessingNovel,
    processingProgress,
    processingStatus,
    processingResult,
    processingError,
    resetNovelProcessing,
    novel,
    isComparing,
    compareMode,
    setIsComparing,
    setCompareMode,
    showDialogueSplit,
    setShowDialogueSplit,
    updateDialogueItem,
  } = useTTSStore();

  const handleReset = () => {
    resetNovelProcessing();
  };

  // 可编辑对话项组件
  const EditableDialogueItem = ({
    item,
    chapterTitle,
    itemIndex
  }: {
    item: DialogueItem;
    chapterTitle: string;
    itemIndex: number;
  }) => {
    // 本地状态，避免每次输入都触发全局状态更新
    const [localSpeaker, setLocalSpeaker] = useState(item.speaker);
    const [localContent, setLocalContent] = useState(item.content);
    const [localEmotion, setLocalEmotion] = useState(item.emotion || 'neutral');

    // 防抖保存函数
    useEffect(() => {
      const timeoutId = setTimeout(() => {
        if (localSpeaker !== item.speaker || localContent !== item.content || localEmotion !== item.emotion) {
          updateDialogueItem(chapterTitle, itemIndex, {
            speaker: localSpeaker,
            content: localContent,
            emotion: localEmotion,
          });
        }
      }, 500); // 500ms 防抖延迟

      return () => clearTimeout(timeoutId);
    }, [localSpeaker, localContent, localEmotion, chapterTitle, itemIndex, item.speaker, item.content, item.emotion, updateDialogueItem]);

    // 当外部数据更新时，同步本地状态
    useEffect(() => {
      setLocalSpeaker(item.speaker);
      setLocalContent(item.content);
      setLocalEmotion(item.emotion || 'neutral');
    }, [item.speaker, item.content, item.emotion]);

    return (
      <div className="p-3 rounded-md bg-neutral-50 border">
        <div className="flex items-start gap-2 mb-2 flex-wrap">
          {/* 角色名输入框 - 使用 flex-shrink-0 防止被压缩 */}
          <input
            type="text"
            value={localSpeaker}
            onChange={(e) => setLocalSpeaker(e.target.value)}
            className="font-semibold text-primary-600 bg-transparent border-0 outline-none focus:ring-1 focus:ring-primary-300 rounded px-1 py-0.5 flex-shrink-0"
            style={{
              minWidth: '60px',
              width: `${Math.max(localSpeaker.length * 12 + 20, 60)}px` // 更精确的宽度计算
            }}
            placeholder="角色名"
          />

          {/* 情绪下拉框 */}
          <select
            value={localEmotion}
            onChange={(e) => setLocalEmotion(e.target.value)}
            className="text-xs font-normal text-neutral-600 bg-white border border-neutral-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-primary-300 flex-shrink-0"
          >
            {EMOTION_OPTIONS.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* 对话内容编辑框 */}
        <textarea
          value={localContent}
          onChange={(e) => setLocalContent(e.target.value)}
          className="w-full mt-1 text-neutral-800 bg-transparent border-0 outline-none focus:ring-1 focus:ring-primary-300 rounded px-1 py-0.5 resize-none"
          style={{ minHeight: '1.5em' }}
          placeholder="对话内容"
          onInput={(e) => {
            const target = e.target as HTMLTextAreaElement;
            target.style.height = 'auto';
            target.style.height = target.scrollHeight + 'px';
          }}
        />
      </div>
    );
  };

  const reconstructedText = useMemo(() => {
    if (!processingResult?.chapters) return '';
    return Object.values(processingResult.chapters)
      .flat()
      .map((item: any) => item.content)
      .join('\n');
  }, [processingResult]);

  const differences = useMemo(() => {
    if (!isComparing) return [];

    let baseText = '';
    if (compareMode === 'original') {
      // 与原文对比：移除原始文本中的数字标记 (如 '1\n')，以便更准确地比较
      baseText = novel.content.replace(/^\d+\s*$/gm, '');
    } else {
      // 与最终结果对比：使用重构的文本作为基准
      baseText = reconstructedText;
    }

    const targetText =
      compareMode === 'original'
        ? reconstructedText
        : novel.content.replace(/^\d+\s*$/gm, '');

    return diffWords(baseText, targetText, {
      ignoreWhitespace: true,
    });
  }, [isComparing, compareMode, novel.content, reconstructedText]);

  return (
    <div className="flex flex-col h-full bg-neutral-50 border-l border-neutral-200">
      <div className="p-2 border-b border-neutral-200 flex justify-between items-center pl-[16px] pr-[16px] pt-[4px] pb-[4px]">
        <h2 className="text-sm font-semibold">AI 处理结果</h2>
        <div className="flex items-center space-x-4">
          {processingResult && (
            <>
              <label className="flex h-8 items-center cursor-pointer gap-2 px-2 rounded-lg hover:bg-neutral-100">
                <span className="text-sm">对比差异</span>
                <input
                  type="checkbox"
                  className="toggle toggle-sm toggle-primary"
                  checked={isComparing}
                  onChange={(e) => setIsComparing(e.target.checked)}
                />
              </label>
              <label
                className="flex h-8 items-center cursor-pointer gap-2 px-2 rounded-lg hover:bg-neutral-100"
              >
                <span className="text-sm">显示对话分割</span>
                <input
                  type="checkbox"
                  className="toggle toggle-sm toggle-primary"
                  checked={showDialogueSplit}
                  onChange={(e) => setShowDialogueSplit(e.target.checked)}
                />
              </label>
            </>
          )}
          <button onClick={handleReset} className="btn btn-ghost btn-sm">
            关闭
          </button>
        </div>
      </div>

      <div className="grow overflow-y-auto p-[8px]">
        {processingError ? (
          <div className="alert alert-error">
            <div className="flex-1">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                className="w-6 h-6 mx-2 stroke-current"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636"
                ></path>
              </svg>
              <label>{processingError}</label>
            </div>
          </div>
        ) : isProcessingNovel || processingResult ? (
          <div className="space-y-4">
            {isProcessingNovel && (
              <>
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    处理状态
                  </label>
                  <p className="text-sm font-medium text-primary-600">
                    {processingStatus}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    处理进度
                  </label>
                  <progress
                    className="progress progress-primary w-full"
                    value={processingProgress}
                    max="100"
                  ></progress>
                </div>
              </>
            )}

            {processingResult && (
              <div>
                <label className="text-sm font-medium text-gray-700">
                  {showDialogueSplit
                    ? '对话分割结果'
                    : isComparing
                      ? `差异对比结果 (${compareMode === 'original' ? '与原文对比' : '与最终结果对比'})`
                      : '对话分割结果'}
                </label>
                <div className="w-full bg-white rounded-md p-4 text-sm border space-y-4 max-h-[calc(100vh-220px)] overflow-y-auto">
                  {showDialogueSplit ? (
                    // 显示对话分割结果（原来的最终结果）
                    Object.entries(processingResult.chapters || {}).map(
                      ([chapterTitle, chapterContent], chapterIndex) => {
                        if (!Array.isArray(chapterContent)) return null;

                        return (
                          <div key={chapterIndex} className="space-y-3">
                            <h3 className="text-base font-semibold border-b pb-2 mb-2">
                              {chapterTitle}
                            </h3>
                            {(chapterContent as DialogueItem[]).map(
                              (item, itemIndex) => (
                                <EditableDialogueItem
                                  key={itemIndex}
                                  item={item}
                                  chapterTitle={chapterTitle}
                                  itemIndex={itemIndex}
                                />
                              )
                            )}
                          </div>
                        );
                      }
                    )
                  ) : isComparing ? (
                    <div className="whitespace-pre-wrap leading-relaxed">
                      {differences.map((part: Change, index: number) => {
                        const style = {
                          backgroundColor: part.added
                            ? 'rgba(67, 177, 88, 0.2)'
                            : part.removed
                              ? 'rgba(239, 68, 68, 0.2)'
                              : 'transparent',
                          textDecoration: part.removed
                            ? 'line-through'
                            : 'none',
                          padding: '2px 0',
                          borderRadius: '3px',
                        };
                        return (
                          <span key={index} style={style}>
                            {part.value}
                          </span>
                        );
                      })}
                    </div>
                  ) : (
                    Object.entries(processingResult.chapters || {}).map(
                      ([chapterTitle, chapterContent], chapterIndex) => {
                        // 确保 chapterContent 是一个数组，以防数据格式意外错误
                        if (!Array.isArray(chapterContent)) {
                          return null;
                        }

                        return (
                          <div key={chapterIndex} className="space-y-3">
                            <h3 className="text-base font-semibold border-b pb-2 mb-2">
                              {chapterTitle}
                            </h3>
                            {(chapterContent as DialogueItem[]).map(
                              (item, itemIndex) => (
                                <EditableDialogueItem
                                  key={itemIndex}
                                  item={item}
                                  chapterTitle={chapterTitle}
                                  itemIndex={itemIndex}
                                />
                              )
                            )}
                          </div>
                        );
                      }
                    )
                  )}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center text-neutral-500 pt-16">
            <span className="loading loading-spinner loading-lg"></span>
            <p className="mt-4">等待处理任务...</p>
          </div>
        )}
      </div>
    </div>
  );
}
