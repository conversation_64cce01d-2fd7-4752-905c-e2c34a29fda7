import { Refresh<PERSON>w, SlidersHorizontal, Cog } from 'lucide-react';
import { useTTSStore } from '../store/ttsStore';
import Slider from './Slider';

export default function TTSSettingsPanel() {
  const {
    settings,
    availableEngines,
    isLoadingEngines,
    engineError,
    updateSettings,
    getCurrentEngineModels,
    getCurrentEngine,
  } = useTTSStore();

  const currentEngineModels = getCurrentEngineModels();
  const currentEngine = getCurrentEngine();

  // 处理重试
  const handleRetry = async () => {
    if (window.__ttsRefetch) {
      await window.__ttsRefetch();
    }
  };

  // 检查当前引擎是否有特定参数（不包括基础语音参数）
  const hasEngineSpecificParams =
    currentEngine && Object.keys(currentEngine.parameters || {}).length > 0;

  return (
    <div className="panel animate-in" data-oid="ejx3gqv">
      <div
        className="panel-header p-[16px] pt-[8px] pb-[8px]"
        data-oid="mza:78q"
      >
        <h2
          className="panel-title text-sm flex items-center"
          data-oid="v4v9lqa"
        >
          <SlidersHorizontal size={12} className="mr-1" data-oid="pn3jq86" />
          TTS设置
        </h2>
        {currentEngine && (
          <div className="text-[10px] text-neutral-500" data-oid="1a0xecu">
            {currentEngine.description}
          </div>
        )}
      </div>

      <div className="panel-body space-y-6" data-oid="pa59lfl">
        {/* 错误提示 - 显示在顶部但不阻止界面 */}
        {engineError && (
          <div className="alert alert-warning" data-oid="kq0eni:">
            <div className="flex flex-col gap-2" data-oid="yn2x39r">
              <span className="text-sm" data-oid="5rt9s2z">
                ⚠️ 后端服务连接失败，TTS功能暂不可用
              </span>
              <button
                className="btn btn-sm btn-ghost"
                onClick={handleRetry}
                disabled={isLoadingEngines}
                data-oid="zjwv_:."
              >
                {isLoadingEngines ? (
                  <>
                    <span
                      className="loading loading-spinner loading-xs"
                      data-oid="trn7hd:"
                    ></span>
                    重试中...
                  </>
                ) : (
                  <span className="flex items-center" data-oid="g0sz-v5">
                    <RefreshCw size={14} className="mr-1" data-oid="zbd76e6" />
                    重试连接
                  </span>
                )}
              </button>
            </div>
          </div>
        )}

        {/* 加载状态 */}
        {isLoadingEngines && (
          <div
            className="flex items-center justify-center py-4"
            data-oid="ok57iuj"
          >
            <div
              className="loading loading-spinner loading-md"
              data-oid="l-te330"
            ></div>
            <span className="ml-2" data-oid="5n38t_w">
              加载引擎数据...
            </span>
          </div>
        )}

        {/* TTS引擎选择 */}
        <div data-oid="9v82dr3">
          <label className="label" data-oid="2kkkhh7">
            TTS引擎
          </label>
          <select
            value={settings.engine}
            onChange={(e) => {
              const newEngine = e.target.value;
              const engineData = availableEngines.find(
                (engine) => engine.id === newEngine
              );
              const firstModel = engineData?.models[0] || '';
              updateSettings({
                engine: newEngine,
                model: firstModel,
                parameters: {}, // 重置参数
              });
            }}
            className="select w-full p-[6px] text-xs"
            disabled={
              Boolean(engineError) ||
              isLoadingEngines ||
              availableEngines.length === 0
            }
            data-oid="uzgcqnc"
          >
            {availableEngines.length === 0 ? (
              <option disabled data-oid="2cctb10">
                暂无可用引擎
              </option>
            ) : (
              availableEngines.map((engine) => (
                <option
                  key={engine.id}
                  value={engine.id}
                  disabled={!engine.is_available}
                  data-oid="utu3v8a"
                >
                  {engine.name} {!engine.is_available && '(不可用)'}
                </option>
              ))
            )}
          </select>
          {currentEngine && (
            <div className="text-xs text-neutral-500 mt-1" data-oid="t714jv_">
              支持语言: {currentEngine.supported_languages.join(', ')}
            </div>
          )}
        </div>

        {/* TTS模型选择 */}
        <div data-oid="9u:r-xd">
          <label className="label" data-oid="x46aecd">
            TTS模型
          </label>
          <select
            value={settings.model}
            onChange={(e) => updateSettings({ model: e.target.value })}
            className="select w-full p-[6px] text-xs"
            disabled={
              Boolean(engineError) ||
              isLoadingEngines ||
              currentEngineModels.length === 0
            }
            data-oid="_6oh240"
          >
            {currentEngineModels.length === 0 ? (
              <option disabled data-oid="7zkp_s9">
                暂无可用模型
              </option>
            ) : (
              currentEngineModels.map((model) => (
                <option key={model} value={model} data-oid="4ae112p">
                  {model}
                </option>
              ))
            )}
          </select>
        </div>

        {/* 引擎参数 */}
        {hasEngineSpecificParams && !engineError && (
          <div className="space-y-4" data-oid="8zokty2">
            <div
              className="text-sm font-medium text-neutral-700 flex items-center gap-2"
              data-oid="7s.97fd"
            >
              <Cog size={16} data-oid="edxi6yc" />
              <span data-oid="-ckf:cc">参数</span>
            </div>

            {Object.entries(currentEngine.parameters).map(
              ([paramName, paramConfig]) => {
                // 检查参数依赖关系
                if (paramConfig.depends_on) {
                  const dependencyMet = Object.entries(
                    paramConfig.depends_on
                  ).every(([depParam, depValue]) => {
                    const currentDepValue =
                      settings.parameters?.[depParam] ??
                      currentEngine.parameters[depParam]?.default;
                    return currentDepValue === depValue;
                  });

                  if (!dependencyMet) {
                    return null; // 依赖条件不满足，不显示此参数
                  }
                }

                const currentValue =
                  settings.parameters?.[paramName] ?? paramConfig.default;

                if (
                  paramConfig.type === 'bool' &&
                  paramConfig.control === 'switch'
                ) {
                  return (
                    <fieldset
                      key={paramName}
                      className="fieldset"
                      data-oid="xew4byh"
                    >
                      <label
                        className="label cursor-pointer"
                        data-oid="3w89.v_"
                      >
                        <span data-oid="qozu5l8">{paramConfig.label}</span>
                        <input
                          type="checkbox"
                          className="toggle toggle-primary toggle-sm"
                          checked={Boolean(currentValue)}
                          onChange={(e) => {
                            updateSettings({
                              parameters: {
                                ...settings.parameters,
                                [paramName]: e.target.checked,
                              },
                            });
                          }}
                          disabled={isLoadingEngines}
                          data-oid="57phmq0"
                        />
                      </label>
                      <div
                        className="text-xs text-neutral-500"
                        data-oid="hn4n459"
                      >
                        {paramConfig.description}
                      </div>
                    </fieldset>
                  );
                }

                if (
                  (paramConfig.type === 'int' ||
                    paramConfig.type === 'float') &&
                  paramConfig.control === 'slider'
                ) {
                  const [min, max] = paramConfig.range as number[];
                  return (
                    <div key={paramName} data-oid="iufuhib">
                      <Slider
                        label={paramConfig.label}
                        value={currentValue}
                        min={min}
                        max={max}
                        step={paramConfig.step || 1}
                        unit={paramConfig.unit || ''}
                        onChange={(value) =>
                          updateSettings({
                            parameters: {
                              ...settings.parameters,
                              [paramName]: value,
                            },
                          })
                        }
                        data-oid="8-w16mb"
                      />

                      <div
                        className="text-xs text-neutral-500 mt-1"
                        data-oid="bqzz4a3"
                      >
                        {paramConfig.description}
                      </div>
                    </div>
                  );
                }

                return null;
              }
            )}
          </div>
        )}
      </div>
    </div>
  );
}
