interface SliderProps {
  label: string;
  value: number;
  min: number;
  max: number;
  step: number;
  unit?: string;
  onChange: (value: number) => void;
}

export default function Slider({
  label,
  value,
  min,
  max,
  step,
  unit = '',
  onChange,
}: SliderProps) {
  const formatValue = (val: number) => {
    if (unit === '%') {
      return `${Math.round(val)}${unit}`;
    }
    if (unit === 'x') {
      return `${val.toFixed(1)}${unit}`;
    }
    return `${val}${unit}`;
  };

  // 计算滑块的百分比位置
  const percentage = ((value - min) / (max - min)) * 100;

  return (
    <div data-oid="_b:m0l2">
      <div
        className="flex items-center justify-between mb-2"
        data-oid="jvm_ptb"
      >
        <label className="label mb-0" data-oid="0pupiq4">
          {label}
        </label>
        <div
          className="badge badge-primary text-xs font-mono"
          data-oid="k-rt9z2"
        >
          {formatValue(value)}
        </div>
      </div>

      <div className="relative" data-oid="r9k6rn2">
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={value}
          onChange={(e) => onChange(Number(e.target.value))}
          className="slider w-full"
          style={{
            background: `linear-gradient(to right, rgb(37 99 235) 0%, rgb(37 99 235) ${percentage}%, rgb(229 231 235) ${percentage}%, rgb(229 231 235) 100%)`,
          }}
          data-oid="8oi1ner"
        />

        {/* 数值标记 */}
        <div
          className="flex justify-between text-xs text-neutral-400 mt-1"
          data-oid="s.e7u4z"
        >
          <span data-oid="ny86wj1">{formatValue(min)}</span>
          <span data-oid="-97ch9a">{formatValue(max)}</span>
        </div>
      </div>
    </div>
  );
}
