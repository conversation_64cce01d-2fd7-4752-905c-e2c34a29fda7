import { useState, useRef } from 'react';
import { useTTSStore } from '../store/ttsStore';
import TitleBar from '../components/TitleBar';
import TTSSettingsPanel from '../components/TTSSettingsPanel';
import CharacterConfigPanel from '../components/CharacterConfigPanel';
import { ttsApi } from '../services/ttsApi';
import { textApi } from '../services/textApi';
import { toast } from 'react-hot-toast';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import ProcessingResultPanel from '../components/ProcessingResultPanel';
import {
  BookOpen,
  ScanFace,
  Sparkles,
  Rocket,
  Hash,
  Space,
  Minus,
  FileX,
} from 'lucide-react';

export default function TTSPage() {
  const {
    novel,
    updateNovel,
    setProcessing,
    isProcessing,
    settings,
    updateAudioPlayer,
    setCurrentAudioUrl,
    showSplitView,
  } = useTTSStore();

  // 格式化处理状态
  const [isFormatting, setIsFormatting] = useState(false);

  // 格式化文本处理函数
  const handleFormatText = async () => {
    if (!novel.content.trim()) {
      toast.error('请先输入小说内容');
      return;
    }

    setIsFormatting(true);

    try {
      // 使用新的format接口参数结构
      const result = await textApi.formatText({
        content: novel.content,
        max_line_width: 80,
        enable_paragraph_indent: false,
        indent_spaces: 4,
        enable_chapter_formatting: true,
        remove_extra_spaces: true,
        normalize_line_breaks: true,
        // 新增小说特定参数
        novel_type: 'auto', // 自动检测小说类型
        enable_dialogue_formatting: true,
        enable_chapter_detection: true,
        normalize_quotes: true,
        enable_character_preprocessing: false, // 暂时关闭角色识别以提高性能
        preserve_chapter_numbers: true,
      });

      if (result.success && result.data?.formatted_content) {
        updateNovel({ content: result.data.formatted_content });

        const originalLength = result.data.original_length || 0;
        const formattedLength = result.data.formatted_length || 0;
        const reduction = originalLength - formattedLength;
        const processingTime = result.data.processing_time || 0;

        // 显示格式化结果信息
        let message = '智能格式化完成！';

        if (result.data.detected_novel_type) {
          const typeNames = { modern: '现代', ancient: '古代', mixed: '混合' };
          message += `检测到${typeNames[result.data.detected_novel_type as keyof typeof typeNames] || result.data.detected_novel_type}小说。`;
        }

        if (result.data.chapter_count && result.data.chapter_count > 0) {
          message += `发现${result.data.chapter_count}个章节。`;
        }

        if (result.data.format_statistics) {
          const stats = result.data.format_statistics;
          if (stats.dialogue_count > 0) {
            message += `包含${stats.dialogue_count}段对话。`;
          }
        }

        if (reduction > 0) {
          message += `优化了${reduction}个字符。`;
        }

        message += `处理用时${processingTime.toFixed(2)}秒。`;

        toast.success(message);

        // 如果检测到角色信息，可以在此处处理
        if (
          result.data.character_hints &&
          result.data.character_hints.length > 0
        ) {
          console.log('检测到潜在角色:', result.data.character_hints);
        }
      } else {
        toast.error(result.error || '文本格式化失败');
      }
    } catch (error) {
      console.error('文本格式化失败:', error);
      toast.error('文本格式化失败，请检查网络连接');
    } finally {
      setIsFormatting(false);
    }
  };

  // 删除章节符：删除单独成行的数字
  const handleRemoveChapterMarkers = () => {
    if (!novel.content.trim()) {
      toast.error('请先输入小说内容');
      return;
    }

    // 匹配单独成行的数字（可能前后有空白字符）
    const processedContent = novel.content.replace(/^\s*\d+\s*$/gm, '');

    updateNovel({ content: processedContent });
    toast.success('章节符号已删除');
  };

  // 删除空格Tab：删除所有的空格或者tab
  const handleRemoveSpacesAndTabs = () => {
    if (!novel.content.trim()) {
      toast.error('请先输入小说内容');
      return;
    }

    // 删除所有空格和Tab字符
    const processedContent = novel.content.replace(/[ \t]/g, '');

    updateNovel({ content: processedContent });
    toast.success('空格和Tab已删除');
  };

  // 删除换行符：删除所有的换行符号，但不包括章节符号的换行符
  const handleRemoveLineBreaks = () => {
    if (!novel.content.trim()) {
      toast.error('请先输入小说内容');
      return;
    }

    // 先标记章节符号行（单独成行的数字），用特殊标记保护
    let processedContent = novel.content.replace(
      /^(\s*\d+\s*)$/gm,
      '___CHAPTER_MARKER___$1___CHAPTER_MARKER___'
    );

    // 删除其他换行符
    processedContent = processedContent.replace(/\n/g, '');

    // 恢复章节符号的换行符
    processedContent = processedContent.replace(
      /___CHAPTER_MARKER___(\s*\d+\s*)___CHAPTER_MARKER___/g,
      '\n$1\n'
    );

    // 清理开头的换行符和多余的连续换行符
    processedContent = processedContent.replace(/^\n+/, ''); // 移除开头的换行符
    processedContent = processedContent.replace(/\n{3,}/g, '\n\n'); // 清理多余的连续换行符

    updateNovel({ content: processedContent });
    toast.success('换行符已删除（保留章节符号）');
  };

  // 删除空行：删除单独的空行
  const handleRemoveEmptyLines = () => {
    if (!novel.content.trim()) {
      toast.error('请先输入小说内容');
      return;
    }

    // 删除所有空行的完整处理
    let processedContent = novel.content;

    // 1. 删除开头的空行
    processedContent = processedContent.replace(/^\n+/, '');

    // 2. 删除结尾的空行
    processedContent = processedContent.replace(/\n+$/, '');

    // 3. 删除中间的连续换行符，只保留单个换行符
    processedContent = processedContent.replace(/\n{2,}/g, '\n');

    updateNovel({ content: processedContent });
    toast.success('空行已删除');
  };

  const handleStartConversion = async () => {
    // 检查输入
    if (!novel.content.trim()) {
      toast.error('请输入小说内容');
      return;
    }

    if (!settings.singleVoiceId) {
      toast.error('请选择语音');
      return;
    }

    setProcessing(true);

    try {
      // 生成输出路径：小说名 + 时间戳（时分秒）
      const now = new Date();
      const timeStamp = now.toTimeString().slice(0, 8).replace(/:/g, ''); // 格式：HHMMSS
      let outputPath: string;

      if (novel.title && novel.title.trim()) {
        // 有小说名：小说名 + 时间戳
        const safeName = novel.title
          .trim()
          .replace(/[/\\?%*:|"<>]/g, '_') // Replace unsafe characters
          .substring(0, 50); // Limit length
        outputPath = `${safeName}_${timeStamp}.wav`;
      } else {
        // 没有小说名：只使用时间戳
        outputPath = `${timeStamp}.wav`;
      }

      const result = await ttsApi.synthesizeText({
        engine_type: settings.engine,
        voice_id_name: settings.singleVoiceId,
        voice_style_name: settings.singleVoiceStyle || '通用',
        text: novel.content,
        output_path: outputPath,
        parameters: settings.parameters,
      });

      if (result.success && result.audio_url) {
        // 更新音频播放器状态
        updateAudioPlayer({
          currentText: novel.content.substring(0, 100) + '...',
          duration: result.duration || 0,
          currentTime: 0,
          isPlaying: false,
        });

        // 设置音频URL到store
        setCurrentAudioUrl(result.audio_url);

        toast.success('语音生成成功！');
      } else {
        toast.error(result.message || '语音生成失败');
      }
    } catch (error) {
      console.error('语音生成失败:', error);
      toast.error('语音生成失败，请检查网络连接');
    } finally {
      setProcessing(false);
    }
  };

  // 用于同步滚动的 Refs
  const leftTextAreaRef = useRef<HTMLTextAreaElement>(null);
  const rightJsonViewRef = useRef<HTMLDivElement>(null);

  const handleScroll = (source: 'left' | 'right') => {
    if (
      source === 'left' &&
      leftTextAreaRef.current &&
      rightJsonViewRef.current
    ) {
      const leftScrollTop = leftTextAreaRef.current.scrollTop;
      const leftScrollHeight = leftTextAreaRef.current.scrollHeight;
      const leftClientHeight = leftTextAreaRef.current.clientHeight;
      const rightScrollHeight = rightJsonViewRef.current.scrollHeight;

      if (leftScrollHeight > leftClientHeight) {
        rightJsonViewRef.current.scrollTop =
          (leftScrollTop / (leftScrollHeight - leftClientHeight)) *
          (rightScrollHeight - rightJsonViewRef.current.clientHeight);
      }
    }
    // 同步滚动从右到左比较复杂，暂时只实现左到右
  };

  return (
    <div
      className="h-screen bg-gradient-app overflow-hidden"
      data-oid="ptljfbm"
    >
      {/* 桌面应用标题栏 */}
      <TitleBar data-oid=".xef49w" />

      {/* 主应用容器 - 使用新的响应式网格布局 */}
      <div
        className="h-[calc(100vh-2.5rem)] grid-app gap-[12px] p-[8px]"
        data-oid="5.b_-o."
      >
        {/* 左侧：TTS设置面板 */}
        <div
          className="space-y-3 overflow-y-auto scrollbar-hide"
          data-oid="kaeg-d:"
        >
          <TTSSettingsPanel data-oid="sr.81.0" />
        </div>

        {/* 中间：主内容区域 */}
        <div className="flex flex-col gap-1 overflow-hidden" data-oid="a_v.oxu">
          {/* 小说信息配置 */}
          <div className="card card-hover animate-in" data-oid="3mmck_o">
            <div
              className="panel-header py-1 pl-[8px] pr-[8px]"
              data-oid="ypg5czg"
            >
              <h2
                className="panel-title text-sm flex items-center gap-2"
                data-oid="2jwqms4"
              >
                <BookOpen className="w-4 h-4" data-oid="vlee8hy" />
                小说配置
              </h2>
              <div
                className="ml-auto flex items-center gap-3"
                data-oid="j2aib0f"
              >
                {isProcessing && (
                  <div
                    className="badge badge-primary animate-pulse"
                    data-oid="gwa40_u"
                  >
                    处理中...
                  </div>
                )}
              </div>
            </div>

            <div className="panel-body p-[4px]" data-oid="6_yhzwx">
              <div
                className="flex items-center justify-between rounded-lg p-[4px] pl-[8px] pr-[8px]"
                data-oid="fe-:x1w"
              >
                {/* 左侧：小说信息 */}
                <div className="flex items-center gap-4" data-oid="4.wm8j7">
                  <div
                    className="flex items-center gap-3 bg-white rounded-lg shadow-sm border border-blue-200/60 hover:border-blue-300/80 transition-all duration-200 h-10 px-4"
                    data-oid=":-g7b7f"
                  >
                    <span
                      className="text-sm font-semibold text-black whitespace-nowrap flex items-center gap-1"
                      data-oid="nm9_jqp"
                    >
                      <ScanFace className="w-4 h-4" data-oid="p-qneer" />
                      名称
                    </span>
                    <div
                      className="h-4 w-px bg-blue-200/70"
                      data-oid="r3wwbmo"
                    ></div>
                    <input
                      type="text"
                      value={novel.title}
                      onChange={(e) => updateNovel({ title: e.target.value })}
                      placeholder="请输入小说名称"
                      className="bg-transparent border-0 outline-none focus:ring-0 px-0 py-0 w-[240px] text-gray-800 text-sm font-medium h-auto"
                      data-oid="1y1obv2"
                    />
                  </div>

                  {/* <div
                                                                       className="flex items-center gap-3 bg-white rounded-md shadow-sm border border-gray-200 h-10 p-[4px]"
                                                                       data-oid="_rkeg2u"
                                                                      >
                                                                       <span
                                                                         className="text-sm font-medium text-blue-700 whitespace-nowrap"
                                                                         data-oid=":9-z3-."
                                                                       >
                                                                         🗂️ 分类
                                                                       </span>
                                                                       <select
                                                                         value={novel.category}
                                                                         onChange={(e) =>
                                                                           updateNovel({ category: e.target.value })
                                                                         }
                                                                         className="select select-sm border-0 bg-transparent focus:ring-0 px-0 py-0 w-[150px] text-gray-700 h-auto"
                                                                         data-oid="vjq-kp0"
                                                                       >
                                                                         <option value="都市言情" data-oid=".:._3db">
                                                                           都市言情
                                                                         </option>
                                                                         <option value="玄幻奇幻" data-oid="o3fmar6">
                                                                           玄幻奇幻
                                                                         </option>
                                                                         <option value="武侠仙侠" data-oid="kc97yvb">
                                                                           武侠仙侠
                                                                         </option>
                                                                         <option value="科幻悬疑" data-oid="k7zz2lu">
                                                                           科幻悬疑
                                                                         </option>
                                                                         <option value="历史军事" data-oid="864c-.j">
                                                                           历史军事
                                                                         </option>
                                                                         <option value="其他" data-oid="vx2oc.-">
                                                                           其他
                                                                         </option>
                                                                       </select>
                                                                      </div> */}
                </div>

                {/* 右侧：功能按钮 */}
                <div className="flex items-center gap-2" data-oid=":4ylz26">
                  {/* 文本清理按钮组 */}
                  <div className="flex items-center gap-2" data-oid="axk81xu">
                    <button
                      className="btn btn-sm btn-outline btn-neutral hover:btn-neutral hover:scale-105 hover:shadow-lg hover:shadow-neutral/20 active:scale-95 transition-all duration-200 ease-out group"
                      onClick={handleRemoveChapterMarkers}
                      data-oid=".13gl_r"
                    >
                      <Hash
                        className="w-4 h-4 group-hover:animate-bounce group-hover:text-primary"
                        data-oid="1h2ux:x"
                      />
                      删除章节符
                    </button>
                    <button
                      className="btn btn-sm btn-outline btn-neutral hover:btn-neutral hover:scale-105 hover:shadow-lg hover:shadow-neutral/20 active:scale-95 transition-all duration-200 ease-out group"
                      onClick={handleRemoveSpacesAndTabs}
                      data-oid="lrpa5hi"
                    >
                      <Space
                        className="w-4 h-4 group-hover:animate-bounce group-hover:text-primary"
                        data-oid="yaf4.64"
                      />
                      删除空格Tab
                    </button>
                    <button
                      className="btn btn-sm btn-outline btn-neutral hover:btn-neutral hover:scale-105 hover:shadow-lg hover:shadow-neutral/20 active:scale-95 transition-all duration-200 ease-out group"
                      onClick={handleRemoveLineBreaks}
                      data-oid="5rk5j7o"
                    >
                      <Minus
                        className="w-4 h-4 group-hover:animate-bounce group-hover:text-primary"
                        data-oid="j2ts3bn"
                      />
                      删除换行符
                    </button>
                    <button
                      className="btn btn-sm btn-outline btn-neutral hover:btn-neutral hover:scale-105 hover:shadow-lg hover:shadow-neutral/20 active:scale-95 transition-all duration-200 ease-out group"
                      onClick={handleRemoveEmptyLines}
                      data-oid="m8l97dt"
                    >
                      <FileX
                        className="w-4 h-4 group-hover:animate-bounce group-hover:text-primary"
                        data-oid="xsr9_4r"
                      />
                      删除空行
                    </button>
                  </div>

                  {/* 分隔线 */}
                  <div
                    className="h-6 w-px bg-gray-300"
                    data-oid="qvvfa1g"
                  ></div>

                  <button
                    className="btn btn-sm btn-outline btn-neutral hover:btn-neutral hover:scale-105 hover:shadow-lg hover:shadow-neutral/20 active:scale-95 transition-all duration-200 ease-out group"
                    onClick={handleFormatText}
                    disabled={isFormatting}
                    data-oid="j0f97zb"
                  >
                    {isFormatting ? (
                      <>
                        <span
                          className="loading loading-spinner loading-xs"
                          data-oid="_r:ue1q"
                        ></span>
                        格式化中...
                      </>
                    ) : (
                      <>
                        <Sparkles
                          className="w-4 h-4 group-hover:animate-spin group-hover:text-primary"
                          data-oid="6v6he5x"
                        />
                        智能格式化
                      </>
                    )}
                  </button>
                  <button
                    className="btn btn-sm btn-primary hover:btn-primary hover:scale-105 hover:shadow-lg hover:shadow-primary/30 active:scale-95 transition-all duration-200 ease-out group"
                    onClick={handleStartConversion}
                    disabled={isProcessing}
                    data-oid="059454l"
                  >
                    {isProcessing ? (
                      <>
                        <span
                          className="loading loading-spinner loading-xs"
                          data-oid="uowc6zl"
                        ></span>
                        生成中...
                      </>
                    ) : (
                      <>
                        <Rocket
                          className="w-4 h-4 group-hover:animate-ping group-hover:text-white"
                          data-oid="juyoy80"
                        />
                        开始生成
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* 小说内容 */}
          <div className="card card-hover grow animate-in" data-oid=".12vxy6">
            <PanelGroup
              direction="horizontal"
              className="h-full"
              data-oid="4ht:fnl"
            >
              <Panel defaultSize={showSplitView ? 50 : 100} data-oid="o.21.sa">
                {showSplitView ? (
                  <div
                    className="flex flex-col h-full bg-neutral-50 border-r border-neutral-200"
                    data-oid="9-qgw-7"
                  >
                    <div
                      className="p-3 border-b border-neutral-200 flex justify-between items-center"
                      data-oid="v:39l2h"
                    >
                      <h2 className="text-sm font-semibold" data-oid=":z46nxl">
                        原文内容
                      </h2>
                    </div>
                    <div className="grow" data-oid="w:e_u6o">
                      <textarea
                        ref={leftTextAreaRef}
                        onScroll={() => handleScroll('left')}
                        value={novel.content}
                        onChange={(e) =>
                          updateNovel({ content: e.target.value })
                        }
                        placeholder="在此处粘贴小说内容..."
                        className="textarea w-full h-full bg-transparent focus:ring-0 text-base leading-relaxed resize-none p-[8px] border-0"
                        readOnly={showSplitView} // 当分栏时变为只读
                        data-oid="2mfbx61"
                      />
                    </div>
                  </div>
                ) : (
                  <textarea
                    ref={leftTextAreaRef}
                    onScroll={() => handleScroll('left')}
                    value={novel.content}
                    onChange={(e) => updateNovel({ content: e.target.value })}
                    placeholder="在此处粘贴小说内容..."
                    className="textarea w-full h-full bg-transparent focus:ring-0 text-base leading-relaxed resize-none p-[8px]"
                    readOnly={showSplitView} // 当分栏时变为只读
                    data-oid="s4pj87g"
                  />
                )}
              </Panel>
              {showSplitView && (
                <>
                  <PanelResizeHandle
                    className="w-2 bg-base-300 hover:bg-primary transition-colors flex items-center justify-center"
                    data-oid="-8ta-o2"
                  >
                    <div
                      className="w-1 h-8 bg-base-content/20 rounded-full"
                      data-oid="hly4zq6"
                    />
                  </PanelResizeHandle>
                  <Panel defaultSize={50} minSize={25} data-oid="mqjh1_0">
                    <div
                      ref={rightJsonViewRef}
                      className="h-full overflow-y-auto"
                      data-oid="nzhykj_"
                    >
                      <ProcessingResultPanel data-oid=".w-yyu3" />
                    </div>
                  </Panel>
                </>
              )}
            </PanelGroup>
          </div>
        </div>

        {/* 右侧：角色配置面板 */}
        <div className="space-y-3" data-oid="6-4quq6">
          <CharacterConfigPanel data-oid="j6.r:5m" />
        </div>
      </div>
    </div>
  );
}
