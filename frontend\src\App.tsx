import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'sonner';
import { Toaster as HotToaster } from 'react-hot-toast';
import TTSPage from './pages/TTSPage';
import TTSEngineProvider from './components/TTSEngineProvider';

// TODO: 创建页面组件后导入
// import NovelEditorPage from './pages/NovelEditorPage';
// import AudioPlayerPage from './pages/AudioPlayerPage';

// 创建 QueryClient 实例
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient} data-oid="4ua40fe">
      <TTSEngineProvider data-oid="p7.njf.">
        <Router data-oid="jvu:vsm">
          <div className="App" data-oid="plq50tv">
            <Routes data-oid="o:vi-52">
              <Route
                path="/"
                element={<TTSPage data-oid="sna:7pp" />}
                data-oid="v1e.81-"
              />

              {/* TODO: 添加更多路由 */}
              {/* <Route path="/editor" element={<NovelEditorPage />} /> */}
              {/* <Route path="/player" element={<AudioPlayerPage />} /> */}
            </Routes>

            {/* 通知组件 */}
            <Toaster position="top-right" data-oid="p9un-8p" />
            <HotToaster position="top-center" data-oid="d1or:om" />
          </div>
        </Router>
      </TTSEngineProvider>
    </QueryClientProvider>
  );
}

export default App;
