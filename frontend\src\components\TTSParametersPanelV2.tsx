import type { TTSParameterValues } from '../types/tts';
import { useTTSParameters } from '../hooks/useTTSParameters';
import DynamicParameterGroup from './DynamicParameterGroup';

interface TTSParametersPanelV2Props {
  /** TTS引擎类型 */
  engineType: string;
  /** 参数变化回调 */
  onParametersChange: (parameters: TTSParameterValues) => void;
  /** 是否显示参数验证错误 */
  showValidationErrors?: boolean;
}

export default function TTSParametersPanelV2({
  engineType,
  onParametersChange,
  showValidationErrors = false,
}: TTSParametersPanelV2Props) {
  const {
    parametersConfig,
    parameterValues,
    showAdvanced,
    loading,
    error,
    setParameterValue,
    resetToDefaults,
    toggleAdvanced,
    validateParameters,
  } = useTTSParameters(engineType);

  // 参数变化处理
  const handleParameterChange = (paramName: string, value: never) => {
    setParameterValue(paramName, value);

    // 更新后的参数值
    const updatedValues = { ...parameterValues, [paramName]: value };
    onParametersChange(updatedValues);
  };

  // 验证结果
  const validation = validateParameters();

  if (loading) {
    return (
      <div className="flex justify-center py-8" data-oid="r6-j0ku">
        <span
          className="loading loading-spinner loading-md"
          data-oid="xt4f4j6"
        ></span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8" data-oid="v3gkr.y">
        <div className="alert alert-error" data-oid="ufkztmj">
          <span data-oid="gd4s.z8">获取参数配置失败: {error}</span>
        </div>
      </div>
    );
  }

  if (Object.keys(parametersConfig).length === 0) {
    return (
      <div className="text-center py-8 text-gray-500" data-oid="p-01mrc">
        <p data-oid="6noit-r">当前引擎没有可配置的参数</p>
      </div>
    );
  }

  return (
    <div className="space-y-4" data-oid="m:82e8l">
      {/* 头部工具栏 */}
      <div
        className="flex justify-between items-center border-b border-gray-200 pb-3"
        data-oid="cm6-mrl"
      >
        <h2 className="text-xl font-semibold text-gray-800" data-oid="22bjt88">
          语音参数
        </h2>
        <div className="flex gap-2" data-oid="2ldno64">
          <label
            className="flex items-center gap-2 cursor-pointer"
            data-oid="1:6m_0y"
          >
            <input
              type="checkbox"
              className="checkbox checkbox-sm"
              checked={showAdvanced}
              onChange={toggleAdvanced}
              data-oid="164:u9l"
            />

            <span className="text-sm text-gray-600" data-oid="any600m">
              显示高级选项
            </span>
          </label>
          <button
            className="btn btn-sm btn-outline"
            onClick={resetToDefaults}
            data-oid="texsen6"
          >
            重置
          </button>
        </div>
      </div>

      {/* 参数验证错误提示 */}
      {showValidationErrors && !validation.isValid && (
        <div className="alert alert-warning" data-oid="4cmaaht">
          <div data-oid="bfgcaff">
            <h3 className="font-bold" data-oid="yuaocjz">
              参数配置有误:
            </h3>
            <ul className="mt-2 list-disc list-inside" data-oid="q-82hpz">
              {validation.errors.map((error, index) => (
                <li key={index} className="text-sm" data-oid="0tnx5be">
                  {error}
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}

      {/* 动态参数组 */}
      <DynamicParameterGroup
        parametersConfig={parametersConfig}
        values={parameterValues}
        onChange={handleParameterChange}
        showAdvanced={showAdvanced}
        data-oid="3-zku:k"
      />

      {/* 参数摘要（可选） */}
      {Object.keys(parameterValues).length > 0 && (
        <div className="mt-6 p-4 bg-gray-50 rounded-lg" data-oid="xtvprux">
          <h3
            className="text-sm font-medium text-gray-700 mb-2"
            data-oid="tw79n8g"
          >
            当前配置
          </h3>
          <div className="text-xs text-gray-600 space-y-1" data-oid="w77dd9o">
            {Object.entries(parameterValues).map(([key, value]) => {
              const config = parametersConfig[key];
              if (!config || (config.advanced && !showAdvanced)) return null;

              const displayValue =
                typeof value === 'boolean'
                  ? value
                    ? '开启'
                    : '关闭'
                  : `${value}${config.unit || ''}`;

              return (
                <div
                  key={key}
                  className="flex justify-between"
                  data-oid="rl34qks"
                >
                  <span data-oid="9ex6xo_">{config.label}:</span>
                  <span className="font-mono" data-oid="16y3fer">
                    {displayValue}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
