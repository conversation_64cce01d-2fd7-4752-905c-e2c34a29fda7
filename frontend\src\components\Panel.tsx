import type { ReactNode } from 'react';

interface PanelProps {
  title: string;
  children: ReactNode;
  className?: string;
  variant?: 'modern' | 'square';
}

export default function Panel({
  title,
  children,
  className = '',
  variant = 'modern',
}: PanelProps) {
  const cardClass = variant === 'square' ? 'card-square' : 'card-modern';
  const headerClass =
    variant === 'square' ? 'panel-header-square' : 'panel-header-modern';

  return (
    <div className={`${cardClass} fade-in ${className}`} data-oid="rknn3.p">
      <div className={headerClass} data-oid="8u4-w_7">
        <span data-oid="x1pnwz-">{title}</span>
      </div>
      <div className="p-5" data-oid="w-7u02i">
        {children}
      </div>
    </div>
  );
}
