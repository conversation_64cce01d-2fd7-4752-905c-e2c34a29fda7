import type { TTSParameter } from '../types/tts';
import Slider from './Slider';

interface DynamicParameterControlProps {
  /** 参数名 */
  paramName: string;
  /** 参数配置 */
  config: TTSParameter;
  /** 当前值 */
  value: any;
  /** 值变化回调 */
  onChange: (paramName: string, value: any) => void;
  /** 是否禁用 */
  disabled?: boolean;
}

export default function DynamicParameterControl({
  paramName,
  config,
  value,
  onChange,
  disabled = false,
}: DynamicParameterControlProps) {
  const handleChange = (newValue: any) => {
    onChange(paramName, newValue);
  };

  // 根据控件类型渲染不同的UI组件
  const renderControl = () => {
    switch (config.control) {
      case 'slider':
        if (config.type === 'int' || config.type === 'float') {
          const [min, max] = config.range as number[];
          return (
            <Slider
              label={config.label}
              value={value || config.default}
              min={min}
              max={max}
              step={config.step || (config.type === 'int' ? 1 : 0.1)}
              unit={config.unit || ''}
              onChange={handleChange}
              data-oid="pg_70lg"
            />
          );
        }
        break;

      case 'switch':
        if (config.type === 'bool') {
          return (
            <fieldset className="fieldset" data-oid="jvn21h:">
              <label className="label cursor-pointer" data-oid="0fq.bod">
                <span data-oid="._ade_t">{config.label}</span>
                <input
                  type="checkbox"
                  className="toggle toggle-primary"
                  checked={value !== undefined ? value : config.default}
                  onChange={(e) => handleChange(e.target.checked)}
                  disabled={disabled}
                  data-oid="0im1-g."
                />
              </label>
              {config.description && (
                <div className="label" data-oid="xer9-k:">
                  <span className="text-sm text-gray-500" data-oid="uew9b24">
                    {config.description}
                  </span>
                </div>
              )}
            </fieldset>
          );
        }
        break;

      case 'select':
        if (config.type === 'select' || config.type === 'str') {
          const options = config.range as string[];
          return (
            <fieldset className="fieldset" data-oid="u-ayeh1">
              <label className="label" data-oid="7qgm50i">
                <span data-oid="wng6:in">{config.label}</span>
              </label>
              <select
                className="select w-full"
                value={value || config.default}
                onChange={(e) => handleChange(e.target.value)}
                disabled={disabled}
                data-oid="zvv4242"
              >
                {options?.map((option) => (
                  <option key={option} value={option} data-oid="x6mqaq1">
                    {option}
                  </option>
                ))}
              </select>
              {config.description && (
                <div className="label" data-oid="fdn:5d.">
                  <span className="text-sm text-gray-500" data-oid="jpoy.3w">
                    {config.description}
                  </span>
                </div>
              )}
            </fieldset>
          );
        }
        break;

      case 'input':
        return (
          <fieldset className="fieldset" data-oid="3froax.">
            <label className="label" data-oid="6xs3hfi">
              <span data-oid="q41luiz">{config.label}</span>
            </label>
            <input
              type={
                config.type === 'int' || config.type === 'float'
                  ? 'number'
                  : 'text'
              }
              className="input w-full"
              value={value || config.default}
              onChange={(e) => {
                const newValue =
                  config.type === 'int'
                    ? parseInt(e.target.value) || config.default
                    : config.type === 'float'
                      ? parseFloat(e.target.value) || config.default
                      : e.target.value;
                handleChange(newValue);
              }}
              disabled={disabled}
              step={config.step}
              min={
                config.type === 'int' || config.type === 'float'
                  ? (config.range as number[])?.[0]
                  : undefined
              }
              max={
                config.type === 'int' || config.type === 'float'
                  ? (config.range as number[])?.[1]
                  : undefined
              }
              data-oid="wmzp9:p"
            />

            {config.description && (
              <div className="label" data-oid="o2zo:_v">
                <span className="text-sm text-gray-500" data-oid="zyf-ydm">
                  {config.description}
                </span>
              </div>
            )}
          </fieldset>
        );

      default:
        return (
          <div className="alert alert-warning" data-oid="p-.i74w">
            <span data-oid=":yp.6id">不支持的控件类型: {config.control}</span>
          </div>
        );
    }
  };

  return (
    <div className="mb-4" data-oid="jmzow8r">
      {renderControl()}
    </div>
  );
}
